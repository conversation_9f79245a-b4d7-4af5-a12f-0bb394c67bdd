var table;

$(document).ready(function () {

    ajaxcsrf();

    table = $("#ujian").DataTable({
        initComplete: function () {
            var api = this.api();
            $('#ujian_filter input')
                .off('.DT')
                .on('keyup.DT', function (e) {
                    api.search(this.value).draw();
                });
        },
        oLanguage: {
            sProcessing: "جاري التحميل...",
            sLengthMenu: "عرض _MENU_ عنصر",
            sZeroRecords: "لا توجد امتحانات متاحة",
            sInfo: "عرض _START_ إلى _END_ من _TOTAL_ امتحان",
            sInfoEmpty: "عرض 0 إلى 0 من 0 امتحان",
            sInfoFiltered: "(مفلتر من _MAX_ امتحان)",
            sSearch: "البحث:",
            oPaginate: {
                sFirst: "الأول",
                sPrevious: "السابق",
                sNext: "التالي",
                sLast: "الأخير"
            }
        },
        processing: true,
        serverSide: true,
        ajax: {
            "url": base_url + "ujian/list_json",
            "type": "POST",
            "error": function(xhr, error, thrown) {
                console.log('DataTable AJAX Error:', error);
                console.log('Response:', xhr.responseText);
                alert('حدث خطأ في تحميل البيانات. يرجى المحاولة مرة أخرى.');
            }
        },
        columns: [
            {
                "data": "id_ujian",
                "orderable": false,
                "searchable": false
            },
            { "data": 'nama_ujian' },
            { "data": 'nama_matkul' },
            { "data": 'nama_dosen' },
            { "data": 'jumlah_soal' },
            { "data": 'waktu' },
            { "data": 'total_attempts' },
            { "data": 'used_attempts' },
            {
                "searchable": false,
                "orderable": false
            },
            {
                "searchable": false,
                "orderable": false
            },
            {
                "searchable": false,
                "orderable": false
            },
            {
                "searchable": false,
                "orderable": false
            }
        ],
        columnDefs: [
            {
                "targets": 6,
                "render": function (data, type, row, meta) {
                    return `<div class="text-center"><span class="badge bg-blue">${data}</span></div>`;
                }
            },
            {
                "targets": 7,
                "render": function (data, type, row, meta) {
                    var color = data > 0 ? 'bg-orange' : 'bg-green';
                    return `<div class="text-center"><span class="badge ${color}">${data}</span></div>`;
                }
            },
            {
                "targets": 8,
                "data": {
                    "id_ujian": "id_ujian",
                    "ada": "ada",
                    "timeout": "timeout"
                },
                "render": function (data, type, row, meta) {
                    var btn;
                    if (data.ada > 0) {
                        btn = `
<a class="btn btn-xs" style="background-color: #555555; border-color: #555555; color: white;" href="${base_url}hasilujian/retest/${data.id_ujian}">
    <i class="fa fa-redo"></i> اعاده الامتحان
</a>`;
                    } else {
                        btn = `<a class="btn btn-xs btn-primary" href="${base_url}ujian/token/${data.id_ujian}">
                                    <i class="fa fa-pencil"></i> الدخول للامتحان
                                </a>`;
                    }
                    return `<div class="text-center">${btn}</div>`;
                }
            },
            {
                "targets": 9,
                "data": {
                    "id_ujian": "id_ujian",
                    "ada": "ada",
                    "timeout": "timeout"
                },
                "render": function (data, type, row, meta) {
                    var btn;
                    if (data.ada > 0) {
                        if (row.timeout == 0) {
                            btn = `<a class="btn btn-xs btn-success" href="${base_url}hasilujian/cetak/${data.id_ujian}">
                                        <i></i> الحصول علي النتيجة
                                    </a>`;
                        } else {
                            btn = `<a class="btn btn-xs btn-success" href="${base_url}hasilujian/mmm/${data.id_ujian}">
                                        <i></i> الدخول علي بوابة النتيجة
                                    </a>`;
                        }
                    } else {
                        btn = "لا يوجد";
                    }
                    return `<div class="text-center">${btn}</div>`;
                }
            },
            {
                "targets": 10,
                "data": {
                    "id_ujian": "id_ujian",
                    "ada": "ada",
                    "timeout": "timeout"
                },
                "render": function (data, type, row, meta) {
                    var btn;
                    if (data.ada) {
                        btn = `
<a class="btn btn-xs btn-info" style="background-color: #00ced1; border-color: #00ced1;" href="${base_url}hasilujian/results/${data.id_ujian}">
    <i class="fa fa-eye"></i> رؤية النتائج السابقة
</a>`;
                    }
                    return `<div class="text-center">${btn}</div>`;
                }
            },
            {
                "targets": 11,
                "data": {
                    "id_ujian": "id_ujian",
                    "ada": "ada",
                    "timeout": "timeout"
                },
                "render": function (data, type, row, meta) {
                    var btn;
                    if (data.ada) {
                        btn = `
<a class="btn btn-xs btn-info" style="background-color: #ff6347; border-color: #ff6347;" href="${base_url}hasilujian/quiz/${data.id_ujian}">
    <i class="fa fa-pencil-alt"></i> حل الامتحان علي صورة كويز
</a>
`;
                    }
                    return `<div class="text-center">${btn}</div>`;
                }
            }
        ],
        order: [
            [1, 'asc']
        ],
        rowId: function (a) {
            return a;
        },
        rowCallback: function (row, data, iDisplayIndex) {
            var info = this.fnPagingInfo();
            var page = info.iPage;
            var length = info.iLength;
            var index = page * length + (iDisplayIndex + 1);
            $('td:eq(0)', row).html(index);
        }
    });
});
