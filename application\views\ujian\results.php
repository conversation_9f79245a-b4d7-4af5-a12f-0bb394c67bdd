
<?php
// استخدام الاتصال الموحد بقاعدة البيانات
require_once APPPATH . '../db.php';

// الحصول على PDO connection
$pdo = getPDOConnection();

// استدعاء القيم من قاعدة البيانات
$results = [];
$mahasiswa_id = isset($mhs) && $mhs ? $mhs->id_mahasiswa : 0;
$ujian_id = isset($ujian) && $ujian ? $ujian->ujian_id : 0;

if ($pdo) {
    // استخدام PDO
    $query = "SELECT * FROM exam WHERE mahasiswa_id = ? AND ujian_id = ?";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$mahasiswa_id, $ujian_id]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
} else {
    // استخدام mysqli
    $conn = getMySQLiConnection();
    $query = "SELECT * FROM exam WHERE mahasiswa_id = ? AND ujian_id = ?";
    $stmt = $conn->prepare($query);
    if ($stmt) {
        $stmt->bind_param("ii", $mahasiswa_id, $ujian_id);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $results[] = $row;
            }
        }
        $stmt->close();
    }
}

// التحقق من وجود بيانات
if (!$results) {
?>
<div class="row">
    <div class="col-sm-12">
        <div class="box box-warning">
            <div class="box-header with-border">
                <h3 class="box-title"><i class="fa fa-exclamation-triangle"></i> لا توجد نتائج</h3>
            </div>
            <div class="box-body text-center">
                <div style="padding: 50px;">
                    <i class="fa fa-file-text-o" style="font-size: 4rem; color: #f39c12; margin-bottom: 20px;"></i>
                    <h4>لم يتم العثور على نتائج</h4>
                    <p class="text-muted">لا توجد نتائج متاحة لهذا الامتحان.</p>
                    <a href="<?= base_url('ujian/list') ?>" class="btn btn-primary">
                        <i class="fa fa-arrow-left"></i> العودة لقائمة الامتحانات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
    return;
}

$current_data = isset($_GET['current_data']) ? $_GET['current_data'] : 0;
?>

<!-- Student Info Cards -->
<div class="row">
    <div class="col-sm-3">
        <div class="alert bg-green">
            <h4>الطالب<i class="pull-right fa fa-user"></i></h4>
            <span class="d-block"><?= isset($mhs) && $mhs ? $mhs->nama : 'غير محدد' ?></span>
        </div>
    </div>
    <div class="col-sm-3">
        <div class="alert bg-blue">
            <h4>الفصل<i class="pull-right fa fa-building-o"></i></h4>
            <span class="d-block"><?= isset($mhs) && $mhs ? $mhs->nama_kelas : 'غير محدد' ?></span>
        </div>
    </div>
    <div class="col-sm-3">
        <div class="alert bg-yellow">
            <h4>القسم<i class="pull-right fa fa-graduation-cap"></i></h4>
            <span class="d-block"><?= isset($mhs) && $mhs ? $mhs->nama_jurusan : 'غير محدد' ?></span>
        </div>
    </div>
    <div class="col-sm-3">
        <div class="alert bg-red">
            <h4>التاريخ<i class="pull-right fa fa-calendar"></i></h4>
            <span class="d-block"><?= date('l, d M Y') ?></span>
        </div>
    </div>
</div>

<!-- Main Results Container -->
<div class="row">
    <div class="col-sm-12">
        <div class="box box-primary">
            <div class="box-header with-border">
                <h3 class="box-title"><i class="fa fa-trophy"></i> نتائج الامتحان</h3>
                <div class="box-tools pull-right">
                    <a href="<?= base_url('ujian/list') ?>" class="btn btn-sm btn-default">
                        <i class="fa fa-arrow-left"></i> العودة لقائمة الامتحانات
                    </a>
                </div>
            </div>
            <div class="box-body">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="box-title"><i class="fa fa-list-alt"></i> المحاولات المتاحة</h3>
                        </div>
                        <div class="box-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th style="width: 30%">رقم المحاولة</th>
                                            <th style="width: 40%">النتائج</th>
                                            <th style="width: 30%">النتيجة المعروضة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($results as $index => $data): ?>
                                        <tr>
                                            <td><span class="badge bg-blue">المحاولة <?php echo $index + 1; ?></span></td>
                                            <td>
                                                <form method="get" action="" style="display: inline;">
                                                    <input type="hidden" name="current_data" value="<?php echo $index; ?>">
                                                    <button type="submit" class="btn btn-sm btn-info">
                                                        <i class="fa fa-eye"></i> عرض النتائج
                                                    </button>
                                                </form>
                                            </td>
                                            <td>
                                                <?php if ($current_data == $index): ?>
                                                    <span class="badge bg-green"><i class="fa fa-check"></i> معروضة حالياً</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                    <?php
                    // استدعاء الداتا الحالية
                    $current_data_row = $results[$current_data];
                    $jml_benar = $current_data_row['jml_benar'];
                    $ide = $current_data_row['id'];
                    $nilai = $current_data_row['nilai'];
                    ?>

                    <div class="box box-success">
                        <div class="box-header with-border">
                            <h3 class="box-title"><i class="fa fa-info-circle"></i> معلومات الامتحان</h3>
                        </div>
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-bordered table-striped">
                                        <tr>
                                            <th style="width: 40%">اسم الامتحان</th>
                                            <td><?= isset($ujian) && $ujian ? $ujian->nama_ujian : 'غير محدد' ?></td>
                                        </tr>
                                        <tr>
                                            <th>الدرجة الكلية للامتحان</th>
                                            <td><span class="badge bg-blue"><?= $jml_benar ?></span></td>
                                        </tr>
                                        <tr>
                                            <th>درجة الطالب</th>
                                            <td><span class="badge bg-green"><?= $nilai ?></span></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-bordered table-striped">
                                        <tr>
                                            <th style="width: 40%">حالة الطالب</th>
                                            <td>
                                                <?php
                                                $status = ($jml_benar == 0 || $nilai < ($jml_benar * 0.5)) ? "راسب" : "ناجح";
                                                $badge_class = ($status == "ناجح") ? "bg-green" : "bg-red";
                                                echo '<span class="badge ' . $badge_class . '">' . $status . '</span>';
                                                ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>تقدير الطالب</th>
                                            <td>
                                                <?php
                                                $percentage = ($nilai / max(1, $jml_benar)) * 100;
                                                $grade = "";
                                                $badge_class = "";

                                                if ($percentage >= 85) {
                                                    $grade = "امتياز";
                                                    $badge_class = "bg-purple";
                                                } elseif ($percentage >= 75) {
                                                    $grade = "جيد جداً";
                                                    $badge_class = "bg-green";
                                                } elseif ($percentage >= 65) {
                                                    $grade = "جيد";
                                                    $badge_class = "bg-blue";
                                                } elseif ($percentage >= 50) {
                                                    $grade = "مقبول";
                                                    $badge_class = "bg-yellow";
                                                } else {
                                                    $grade = "راسب";
                                                    $badge_class = "bg-red";
                                                }

                                                echo '<span class="badge ' . $badge_class . '">' . $grade . '</span>';
                                                echo ' <small>(' . number_format($percentage, 1) . '%)</small>';
                                                ?>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tab 2: Questions and Answers -->
                <div class="tab-pane" id="tab_2">
                    <div class="box box-info">
                        <div class="box-header with-border">
                            <h3 class="box-title"><i class="fa fa-question-circle"></i> الأسئلة والإجابات</h3>
                            <div class="box-tools pull-right">
                                <button type="button" class="btn btn-sm btn-primary" onclick="toggleQuestionList()">
                                    <i class="fa fa-list"></i> قائمة الأسئلة
                                </button>
                            </div>
                        </div>
                        <div class="box-body">
                            <!-- Question Navigation -->
                            <div class="question-list" id="questionList" style="display: none;">
                                <div class="text-center">
                                    <h5><i class="fa fa-list-ol"></i> قائمة الأسئلة</h5>
                                    <ul id="questionNumbers" class="list-inline">
                                        <!-- سيتم ملء هذه القائمة ديناميكياً باستخدام جافاسكريبت -->
                                    </ul>
                                </div>
                            <!-- Questions Content -->
                            <?php
                            try {
                                // استخدام الاتصال الموحد
                                $pdo = getPDOConnection();

                                // استعلام لاسترداد السؤال وإجابة الطالب
                                if (isset($current_data_row) && isset($ujian) && $current_data_row && $ujian) {
                                    $query = "SELECT * FROM exam WHERE id = ? AND ujian_id = ?";
                                    $stmt = $pdo->prepare($query);
                                    $ujian_id = isset($ujian) && $ujian ? $ujian->ujian_id : 0;
                                    $stmt->execute([$ide, $ujian_id]);

                                    $row_number = 1;
                                    // عرض السؤال وإجابة الطالب
                                    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                                        $list_jawaban = explode(',', $row['list_jawaban']);
                                        foreach($list_jawaban as $jawaban) {
                                            $pieces = explode(':', $jawaban);
                                            // استعلام لاسترداد السؤال المطابق للقيمة في $pieces[0]
                                            $query_soal = "SELECT * FROM tb_soal WHERE id_soal = ?";
                                            $stmt_soal = $pdo->prepare($query_soal);
                                            $stmt_soal->execute([$pieces[0]]);
                                            $tb_soal = $stmt_soal->fetch(PDO::FETCH_ASSOC);

                                            if ($tb_soal) {
                                                echo '<div class="question-block panel panel-default" id="question-' . $tb_soal['id_soal'] . '">';
                                                echo '<div class="panel-heading">';
                                                echo '<h4 class="panel-title"><strong>' . $row_number++ . ') <span class="question-text">' . $tb_soal['soal'] . '</span></strong></h4>';

                                                // أدوات التمييز والملاحظات
                                                echo '<div class="tools">';
                                                echo '<span class="tool highlight-tool" data-id="' . $tb_soal['id_soal'] . '" title="تمييز السؤال">🖍️</span>';
                                                echo '<span class="tool note-tool" data-id="' . $tb_soal['id_soal'] . '" title="إضافة ملاحظة">📝</span>';
                                                echo '<span class="tool mark-tool" data-id="' . $tb_soal['id_soal'] . '" title="تمييز بنجمة">⭐</span>';
                                                echo '</div>';
                                                echo '</div>';

                                                // حقل الملاحظات
                                                echo '<div class="note-container" id="note-container-' . $tb_soal['id_soal'] . '" style="display: none;">';
                                                echo '<textarea class="note" placeholder="أضف ملاحظات هنا..." data-id="' . $tb_soal['id_soal'] . '"></textarea>';
                                                echo '</div>';

                                                echo '<div class="panel-body">';

                                                // عرض الاختيارات
                                                $options = ['A', 'B', 'C', 'D'];
                                                foreach ($options as $option) {
                                                    $option_value = $tb_soal['opsi_' . $option];
                                                    $option_file = $tb_soal['file_' . $option];
                                                    $correct_answer = $tb_soal['jawaban'];
                                                    $student_answer = $pieces[1];

                                                    $is_student_answer = ($student_answer == $option);
                                                    $is_correct_answer = ($correct_answer == $option);
                                                    $is_correct = $is_student_answer && $is_correct_answer;

                                                    $class = 'list-group-item';
                                                    if ($is_student_answer && $is_correct_answer) {
                                                        $class .= ' list-group-item-success';
                                                    } elseif ($is_student_answer && !$is_correct_answer) {
                                                        $class .= ' list-group-item-danger';
                                                    } elseif ($is_correct_answer) {
                                                        $class .= ' list-group-item-info';
                                                    }

                                                    echo '<div class="' . $class . '" style="padding: 10px; margin-bottom: 5px;">';
                                                    echo '<div style="display: flex; justify-content: space-between; align-items: center;">';
                                                    echo '<div style="display: flex; align-items: center;">';
                                                    echo '<span class="badge" style="margin-right: 15px;">' . $option . '</span>';
                                                    echo '<span class="option-text" data-id="' . $tb_soal['id_soal'] . '-' . $option . '">' . $option_value . '</span>';
                                                    echo '<span class="tool option-highlight-tool" data-id="' . $tb_soal['id_soal'] . '-' . $option . '" title="تمييز الاختيار" style="margin-left: 10px; cursor: pointer;">🖍️</span>';
                                                    echo '</div>';

                                                    if ($option_file) {
                                                        echo '<br><img src="' . base_url('uploads/bank_soal/' . $option_file) . '" class="img-responsive" style="max-width: 200px; margin-top: 10px;" />';
                                                    }

                                                    if ($is_student_answer) {
                                                        echo $is_correct ? '<span class="pull-left text-success"><i class="fa fa-check"></i> إجابتك صحيحة</span>' : '<span class="pull-left text-danger"><i class="fa fa-times"></i> إجابتك خطأ</span>';
                                                    }

                                                    if ($is_correct_answer && !$is_student_answer) {
                                                        echo '<span class="pull-left text-info"><i class="fa fa-lightbulb-o"></i> الإجابة الصحيحة</span>';
                                                    }

                                                    echo '</div>';
                                                    echo '</div>';
                                                }

                                                // عرض التوضيح إن وجد
                                                if (!empty($tb_soal['ex'])) {
                                                    echo '<div class="alert alert-info">';
                                                    echo '<h5><i class="fa fa-info-circle"></i> توضيح الإجابة:</h5>';
                                                    echo '<p>' . $tb_soal['ex'] . '</p>';
                                                    echo '</div>';
                                                }

                                                echo '</div>';
                                                echo '</div>';
                                            }
                                        }
                                    }
                                } else {
                                    echo '<div class="alert alert-warning"><i class="fa fa-warning"></i> لا توجد بيانات متاحة لعرض الأسئلة والإجابات.</div>';
                                }
                            } catch (Exception $e) {
                                echo '<div class="alert alert-danger"><i class="fa fa-exclamation-triangle"></i> حدث خطأ في تحميل البيانات: ' . $e->getMessage() . '</div>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Custom CSS for Results Page -->
<style>
/* Question Block Styling */
.question-block {
    margin-bottom: 20px;
}

.question-block .panel-heading {
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
    position: relative;
}

.question-block .list-group-item {
    border-left: 4px solid transparent;
}

.question-block .list-group-item-success {
    border-left-color: #5cb85c;
    background-color: #dff0d8;
}

.question-block .list-group-item-danger {
    border-left-color: #d9534f;
    background-color: #f2dede;
}

.question-block .list-group-item-info {
    border-left-color: #5bc0de;
    background-color: #d9edf7;
}

/* Tools Styling */
.tools {
    display: flex;
    gap: 5px;
    margin: 5px 0;
}

.tool {
    cursor: pointer;
    font-size: 14px;
    padding: 2px 5px;
    border-radius: 3px;
    transition: background-color 0.3s;
}

.tool:hover {
    background-color: #e0e0e0;
}

.option-highlight-tool {
    font-size: 12px;
    margin-right: 5px;
}

/* Highlighting */
.highlight {
    background-color: yellow !important;
    padding: 2px 4px;
    border-radius: 3px;
}

/* Marking */
.marked {
    border-left: 4px solid #ffd700 !important;
    background-color: #fffbf0 !important;
}

/* Notes */
.note-container {
    margin: 10px 0;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.note {
    width: 100%;
    min-height: 80px;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 3px;
    resize: vertical;
    font-family: Arial, sans-serif;
    direction: rtl;
    text-align: right;
}

.note:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
}

/* Question List */
.question-list ul {
    padding: 0;
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 5px;
}

.question-list ul li {
    background-color: #3498db;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.question-list ul li:hover {
    transform: scale(1.2);
}

.question-list ul li.active {
    background-color: #2c3e50;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .tools {
        flex-direction: column;
        gap: 2px;
    }

    .question-list ul {
        gap: 3px;
    }

    .question-list ul li {
        width: 25px;
        height: 25px;
        font-size: 12px;
    }
}
</style>

<!-- JavaScript for Results Page -->
<script>
// Toggle question list visibility
function toggleQuestionList() {
    const questionList = document.getElementById('questionList');
    if (questionList.style.display === 'none') {
        questionList.style.display = 'block';
        generateQuestionNumbers();
    } else {
        questionList.style.display = 'none';
    }
}

// Generate question numbers dynamically
function generateQuestionNumbers() {
    const questionBlocks = document.querySelectorAll('.question-block');
    const questionNumbersList = document.getElementById('questionNumbers');
    questionNumbersList.innerHTML = '';

    questionBlocks.forEach(function(block, index) {
        const questionNumberItem = document.createElement('li');
        questionNumberItem.textContent = index + 1;
        questionNumberItem.className = 'btn btn-xs btn-primary';
        questionNumberItem.style.margin = '2px';
        questionNumberItem.addEventListener('click', function() {
            block.scrollIntoView({ behavior: 'smooth' });
            // Activate tab 2 if not already active
            const tab2 = document.querySelector('a[href="#tab_2"]');
            if (tab2) tab2.click();
        });
        questionNumbersList.appendChild(questionNumberItem);
    });
}

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Restore saved data
    restoreHighlights();
    restoreMarks();
    restoreNotes();

    // Highlighting tools
    document.querySelectorAll('.highlight-tool, .option-highlight-tool').forEach(tool => {
        tool.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            let targetElement;

            if (this.classList.contains('highlight-tool')) {
                targetElement = document.querySelector('#question-' + id + ' .question-text');
            } else {
                targetElement = document.querySelector('.option-text[data-id="' + id + '"]');
            }

            if (targetElement) {
                targetElement.classList.toggle('highlight');
                const isHighlighted = targetElement.classList.contains('highlight');
                localStorage.setItem('highlight-' + id, isHighlighted);
            }
        });
    });

    // Marking tools
    document.querySelectorAll('.mark-tool').forEach(tool => {
        tool.addEventListener('click', function() {
            const questionId = this.getAttribute('data-id');
            const questionBlock = document.getElementById('question-' + questionId);
            questionBlock.classList.toggle('marked');

            const isMarked = questionBlock.classList.contains('marked');
            localStorage.setItem('mark-' + questionId, isMarked);
        });
    });

    // Note tools
    document.querySelectorAll('.note-tool').forEach(tool => {
        tool.addEventListener('click', function() {
            const questionId = this.getAttribute('data-id');
            const noteContainer = document.getElementById('note-container-' + questionId);

            if (noteContainer.style.display === 'none') {
                noteContainer.style.display = 'block';
            } else {
                noteContainer.style.display = 'none';
            }
        });
    });

    // Save notes
    document.querySelectorAll('.note').forEach(note => {
        note.addEventListener('input', function() {
            const questionId = this.getAttribute('data-id');
            const noteText = this.value;
            localStorage.setItem('note-' + questionId, noteText);
        });
    });
});

// Restore functions
function restoreHighlights() {
    document.querySelectorAll('.question-block').forEach(block => {
        const questionId = block.id.split('-')[1];

        const isHighlighted = localStorage.getItem('highlight-' + questionId) === 'true';
        if (isHighlighted) {
            const questionText = block.querySelector('.question-text');
            if (questionText) questionText.classList.add('highlight');
        }

        block.querySelectorAll('.option-text').forEach(optionText => {
            const optionId = optionText.getAttribute('data-id');
            const isOptionHighlighted = localStorage.getItem('highlight-' + optionId) === 'true';
            if (isOptionHighlighted) {
                optionText.classList.add('highlight');
            }
        });
    });
}

function restoreMarks() {
    document.querySelectorAll('.question-block').forEach(block => {
        const questionId = block.id.split('-')[1];
        const isMarked = localStorage.getItem('mark-' + questionId) === 'true';
        if (isMarked) {
            block.classList.add('marked');
        }
    });
}

function restoreNotes() {
    document.querySelectorAll('.note').forEach(note => {
        const questionId = note.getAttribute('data-id');
        const savedNote = localStorage.getItem('note-' + questionId);
        if (savedNote) {
            note.value = savedNote;
            const noteContainer = document.getElementById('note-container-' + questionId);
            if (noteContainer) {
                noteContainer.style.display = 'block';
            }
        }
    });
}

// Clear all saved data
function clearAllData() {
    if (confirm('هل أنت متأكد من مسح جميع التمييزات والملاحظات؟')) {
        Object.keys(localStorage).forEach(key => {
            if (key.startsWith('highlight-') || key.startsWith('mark-') || key.startsWith('note-')) {
                localStorage.removeItem(key);
            }
        });
        location.reload();
    }
}
</script>
