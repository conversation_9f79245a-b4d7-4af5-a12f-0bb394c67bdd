<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الامتحان</title>
    <link rel="stylesheet" href="<?=base_url()?>assets/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?=base_url()?>assets/dist/css/font-awesome.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            padding: 20px 0;
        }

        .exam-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }

        .prayer-text {
            background: rgba(255, 255, 255, 0.95);
            color: #d32f2f;
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: bold;
            font-size: 16px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        /* Question Navigation Panel */
        .question-nav-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            margin-bottom: 20px;
            height: fit-content;
            position: sticky;
            top: 20px;
        }

        .question-nav-title {
            color: #2c3e50;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(45px, 1fr));
            gap: 8px;
            margin-bottom: 20px;
        }

        .question-btn {
            width: 45px;
            height: 45px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .question-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* Question Status Colors */
        .question-btn.unanswered {
            background: #ecf0f1;
            color: #7f8c8d;
            border: 2px solid #bdc3c7;
        }

        .question-btn.answered {
            background: #27ae60;
            color: white;
            border: 2px solid #229954;
        }

        .question-btn.answered::after {
            content: '✓';
            position: absolute;
            top: -5px;
            right: -5px;
            background: #fff;
            color: #27ae60;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .question-btn.doubtful {
            background: #f39c12;
            color: white;
            border: 2px solid #e67e22;
        }

        .question-btn.doubtful::after {
            content: '?';
            position: absolute;
            top: -5px;
            right: -5px;
            background: #fff;
            color: #f39c12;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 12px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .question-btn.current {
            background: #3498db;
            color: white;
            border: 3px solid #2980b9;
            transform: scale(1.1);
        }

        /* Legend */
        .legend {
            background: rgba(248, 249, 250, 0.9);
            border-radius: 10px;
            padding: 15px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-left: 10px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        /* Question Panel */
        .question-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }

        .question-header {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .question-number {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
        }

        .time-remaining {
            background: #e74c3c;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .question-body {
            padding: 30px;
            min-height: 400px;
        }

        .question-footer {
            background: #f8f9fa;
            padding: 20px;
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            text-decoration: none;
        }

        .btn-back {
            background: #6c757d;
            color: white;
        }

        .btn-doubt {
            background: #f39c12;
            color: white;
        }

        .btn-next {
            background: #17a2b8;
            color: white;
        }

        .btn-finish {
            background: #dc3545;
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .exam-container {
                padding: 0 10px;
            }

            .question-nav-panel {
                position: static;
                margin-bottom: 15px;
            }

            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
                gap: 6px;
            }

            .question-btn {
                width: 40px;
                height: 40px;
                font-size: 12px;
            }

            .question-header {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .question-body {
                padding: 20px 15px;
            }

            .question-footer {
                padding: 15px;
                gap: 8px;
            }

            .action-btn {
                padding: 10px 15px;
                font-size: 14px;
            }
        }

        @media (max-width: 480px) {
            .question-grid {
                grid-template-columns: repeat(6, 1fr);
            }

            .action-btn {
                padding: 8px 12px;
                font-size: 12px;
            }

            .prayer-text {
                font-size: 14px;
                padding: 10px;
            }

            .question-nav-title {
                font-size: 16px;
            }

            .legend-item {
                font-size: 12px;
            }
        }

        /* Custom Radio Button Styles */
        .funkyradio-success input[type="radio"] {
            display: none;
        }

        .funkyradio-success label {
            display: block;
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .funkyradio-success label:hover {
            background: #e9ecef;
            border-color: #3498db;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .funkyradio-success input[type="radio"]:checked + label {
            background: #d4edda;
            border-color: #27ae60;
            color: #155724;
        }

        .funkyradio-success input[type="radio"]:checked + label::after {
            content: '✓';
            position: absolute;
            top: 10px;
            left: 10px;
            background: #27ae60;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .huruf_opsi {
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
            float: right;
        }

        /* Question Content Styling */
        .step {
            display: none;
            animation: fadeIn 0.5s ease-in-out;
        }

        .step.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .step h4 {
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .step img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 10px 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
<?php
if(time() >= $soal->waktu_habis)
{
    redirect('ujian/list', 'location', 301);
}
?>

<div class="exam-container">
    <!-- Prayer Text -->
    <div class="prayer-text">
        <i class="fa fa-heart"></i>
        اللهم لا سهل إلا ما جعلته سهلاً، وأنت تجعل الحزن إذا شئت سهلاً
        <i class="fa fa-heart"></i>
    </div>
<?php
// بيانات الاتصال بقاعدة البيانات
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "smartexa_system";

// إنشاء اتصال بقاعدة البيانات
$conn = new mysqli($servername, $username, $password, $dbname);

// التحقق من الاتصال
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// الحصول على بيانات الطالب
$mahasiswa_nim = $conn->real_escape_string($mhs->nim);
$mahasiswa_nam = $conn->real_escape_string($mhs->nama);

// الحصول على User Agent
$user_agent = $_SERVER['HTTP_USER_AGENT'];

// استخراج اسم الجهاز من الـ user agent باستخدام تعبيرات منتظمة
function extractDeviceName($user_agent) {
    if (preg_match('/\((.*?)\)/', $user_agent, $matches)) {
        return $matches[1];
    }
    return 'Unknown Device';
}

$device_name = extractDeviceName($user_agent);

// التحقق من وجود الجهاز بالفعل في قاعدة البيانات بناءً على device_id
$sql_check_device_id = "SELECT * FROM devices WHERE device_id = '$mahasiswa_nim'";
$result_device_id = $conn->query($sql_check_device_id);

if ($result_device_id->num_rows > 0) {
    // الجهاز موجود بالفعل، التحقق من البيانات
    $row_device = $result_device_id->fetch_assoc();
    if ($row_device['user_agent'] == $device_name) {
        echo "";
    } else {
        // توجيه إلى صفحة warning.php
        header("Location: warning.php");
        exit();
    }
} else {
    // الجهاز غير موجود، إضافة البيانات الجديدة
    $sql_insert = "INSERT INTO devices (name, device_id, user_agent) VALUES ('$mahasiswa_nam','$mahasiswa_nim', '$device_name')";
    if ($conn->query($sql_insert) === TRUE) {
        echo "تمت اضافة هذا الجهاز بنجاح";
    } else {
        echo "Error: " . $sql_insert . "<br>" . $conn->error;
    }
}

// إغلاق الاتصال
$conn->close();
?>


    <!-- Main Content Row -->
    <div class="row">
        <!-- Question Navigation Panel -->
        <div class="col-lg-3 col-md-4">
            <div class="question-nav-panel">
                <div class="question-nav-title">
                    <i class="fa fa-list"></i> قائمة الأسئلة
                </div>

                <!-- Question Grid -->
                <div class="question-grid" id="tampil_jawaban">
                    <!-- Questions will be populated by JavaScript -->
                </div>

                <!-- Legend -->
                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background: #ecf0f1; border: 2px solid #bdc3c7;"></div>
                        <span>غير مجاوب</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #27ae60; border: 2px solid #229954;"></div>
                        <span>مجاوب بدون شك</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #f39c12; border: 2px solid #e67e22;"></div>
                        <span>مجاوب مع شك</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #3498db; border: 3px solid #2980b9;"></div>
                        <span>السؤال الحالي</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Question Panel -->
        <div class="col-lg-9 col-md-8">
            <?=form_open('', ['id'=>'ujian'], ['id'=> $id_tes]);?>
            <div class="question-panel">
                <div class="question-header">
                    <div class="question-number">
                        <i class="fa fa-question-circle"></i>
                        السؤال رقم <span id="soalke"></span>
                    </div>
                    <div class="time-remaining">
                        <i class="fa fa-clock-o"></i>
                        الوقت المتبقي: <span class="sisawaktu" data-time="<?=$soal->tgl_selesai?>"></span>
                    </div>
                </div>

                <div class="question-body">
                    <?=$html?>
                </div>

                <div class="question-footer">
                    <a class="action-btn btn-back action back" rel="0" onclick="return back();">
                        <i class="fa fa-chevron-right"></i> الرجوع
                    </a>
                    <a class="action-btn btn-doubt ragu_ragu" rel="1" onclick="return tidak_jawab();">
                        <i class="fa fa-question"></i> شاكك
                    </a>
                    <a class="action-btn btn-next action next" rel="2" onclick="return next();">
                        <i class="fa fa-chevron-left"></i> السؤال التالي
                    </a>
                    <a class="action-btn btn-finish selesai action submit" onclick="return simpan_akhir();">
                        <i class="fa fa-stop"></i> إنهاء الامتحان
                    </a>
                    <input type="hidden" name="jml_soal" id="jml_soal" value="<?=$no; ?>">
                </div>
            </div>
            <?=form_close();?>
        </div>
    </div>
</div>

<!-- Include jQuery and Bootstrap JS -->
<script src="<?=base_url()?>assets/dist/js/jquery.min.js"></script>
<script src="<?=base_url()?>assets/dist/js/bootstrap.min.js"></script>

<script type="text/javascript">
    var base_url        = "<?=base_url(); ?>";
    var id_tes          = "<?=$id_tes; ?>";
    var widget          = $(".step");
    var total_widget    = widget.length;

    // Enhanced Question Navigation
    $(document).ready(function() {
        initializeQuestionNavigation();
        updateQuestionStatus();
    });

    function initializeQuestionNavigation() {
        var questionGrid = $('#tampil_jawaban');
        questionGrid.empty();

        for (var i = 1; i <= total_widget; i++) {
            var questionBtn = $('<button>')
                .addClass('question-btn unanswered')
                .attr('data-question', i)
                .text(i)
                .click(function() {
                    var questionNum = $(this).data('question');
                    goToQuestion(questionNum);
                });

            questionGrid.append(questionBtn);
        }
    }

    function updateQuestionStatus() {
        $('.question-btn').each(function() {
            var questionNum = $(this).data('question');
            var questionWidget = $('#widget_' + questionNum);
            var isAnswered = questionWidget.find('input[type="radio"]:checked').length > 0;
            var isDoubtful = $('#rg_' + questionNum).val() === 'Y';

            $(this).removeClass('unanswered answered doubtful current');

            if (questionNum == current_widget + 1) {
                $(this).addClass('current');
            } else if (isAnswered) {
                if (isDoubtful) {
                    $(this).addClass('doubtful');
                } else {
                    $(this).addClass('answered');
                }
            } else {
                $(this).addClass('unanswered');
            }
        });
    }

    function goToQuestion(questionNum) {
        if (questionNum >= 1 && questionNum <= total_widget) {
            current_widget = questionNum - 1;
            showQuestion(current_widget);
            updateQuestionStatus();
        }
    }

    function showQuestion(index) {
        widget.hide();
        widget.eq(index).show();
        $('#soalke').text(index + 1);
        updateQuestionStatus();
    }

    // Override original functions to update question status
    var originalSimpanSementara = window.simpan_sementara;
    window.simpan_sementara = function() {
        if (originalSimpanSementara) {
            var result = originalSimpanSementara();
            setTimeout(updateQuestionStatus, 100);
            return result;
        }
    };

    var originalTidakJawab = window.tidak_jawab;
    window.tidak_jawab = function() {
        if (originalTidakJawab) {
            var result = originalTidakJawab();
            setTimeout(updateQuestionStatus, 100);
            return result;
        }
    };

    var originalNext = window.next;
    window.next = function() {
        if (originalNext) {
            var result = originalNext();
            setTimeout(updateQuestionStatus, 100);
            return result;
        }
    };

    var originalBack = window.back;
    window.back = function() {
        if (originalBack) {
            var result = originalBack();
            setTimeout(updateQuestionStatus, 100);
            return result;
        }
    };

    // Initialize first question
    $(document).ready(function() {
        setTimeout(function() {
            showQuestion(0);
            updateQuestionStatus();
        }, 500);
    });
</script>

<script src="<?=base_url()?>assets/dist/js/app/ujian/sheet.js"></script>

</body>
</html>
