<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Hasil<PERSON>jian extends CI_Controller {

	public function __construct(){
		parent::__construct();
		if (!$this->ion_auth->logged_in()){
			redirect('auth');
		}
		
		$this->load->library(['datatables']);// Load Library Ignited-Datatables
		$this->load->model('Master_model', 'master');
		$this->load->model('Ujian_model', 'ujian');
		
		$this->user = $this->ion_auth->user()->row();
	}

	public function output_json($data, $encode = true)
	{
		if($encode) $data = json_encode($data);
		$this->output->set_content_type('application/json')->set_output($data);
	}

	public function data()
	{
		$nip_dosen = null;
		
		if( $this->ion_auth->in_group('Lecturer') ) {
			$nip_dosen = $this->user->username;
		}

		$this->output_json($this->ujian->getHasilUjian($nip_dosen), false);
	}

	public function NilaiMhs($id)
	{
		$this->output_json($this->ujian->HslUjianById($id, true), false);
	}

	public function index()
	{
		$data = [
			'user' => $this->user,
			'judul'	=> 'الامتحانات',
			'subjudul'=> 'نتائج الامتحانات',
		];
		$this->load->view('_templates/dashboard/_header.php', $data);
		$this->load->view('ujian/hasil');
		$this->load->view('_templates/dashboard/_footer.php');
	}
	
	public function detail($id)
	{
		$ujian = $this->ujian->getUjianById($id);
		$nilai = $this->ujian->bandingNilai($id);

		$data = [
			'user' => $this->user,
			'judul'	=> 'الامتحانات',
			'subjudul'=> 'تفاصيل نتائج الامتحانات',
			'ujian'	=> $ujian,
			'nilai'	=> $nilai
		];

		$this->load->view('_templates/dashboard/_header.php', $data);
		$this->load->view('ujian/detail_hasil');
		$this->load->view('_templates/dashboard/_footer.php');
	}

	public function cetak($id)
	{
		$user = $this->ion_auth->user()->row();
		$mhs 	= $this->ujian->getIdMahasiswa($user->username);
		$hasil 	= $this->ujian->HslUjian($id, $mhs->id_mahasiswa)->row();
		$ujian 	= $this->ujian->getUjianById($id);

		$data = [
			'user' 		=> $user,
			'judul'		=> 'نتائج الامتحان',
			'subjudul'	=> 'تفاصيل نتيجة الامتحان',
			'ujian' => $ujian,
			'hasil' => $hasil,
			'mhs'	=> $mhs
		];

		$this->load->view('_templates/dashboard/_header.php', $data);
		$this->load->view('ujian/cetak', $data);
		$this->load->view('_templates/dashboard/_footer.php');
	}
	
public function mmm($id)
	{
		$this->load->library('Pdf');

		$user = $this->ion_auth->user()->row();
		$mhs 	= $this->ujian->getIdMahasiswa($user->username);
		$hasil 	= $this->ujian->HslUjian($id, $mhs->id_mahasiswa)->row();
		$ujian 	= $this->ujian->getUjianById($id);
		$list_soal = $this->ujian->getSoal($id);



		$data = [
			'ujian' => $ujian,
			'hasil' => $hasil,
			'mhs'	=> $mhs,
			'list_soal' => $list_soal,

		];

		$this->load->view('ujian/mmm', $data);
	}
	public function retest($id)
	{

        $user = $this->ion_auth->user()->row();
		$mhs 	= $this->ujian->getIdMahasiswa($user->username);
		$hasil 	= $this->ujian->HslUjian($id, $mhs->id_mahasiswa)->row();
		$ujian 	= $this->ujian->getUjianById($id);
		$data = [
			'user' 		=> $user,
			'judul'		=> 'الامتحان',
			'subjudul'	=> 'اعاده الامتحان',
			'ujian' => $ujian,
			'hasil' => $hasil,
			'mhs'	=> $mhs

		];
		$this->load->view('_templates/topnav/_header.php', $data);
		$this->load->view('ujian/retest', $data);
		$this->load->view('_templates/topnav/_footer.php');
	}
	public function results($id)
	{
		$user = $this->ion_auth->user()->row();
		$mhs 	= $this->ujian->getIdMahasiswa($user->username);
		$hasil 	= $this->ujian->HslUjian($id, $mhs->id_mahasiswa)->row();
		$ujian 	= $this->ujian->getUjianById($id);

		// Assigning ujian_id to match the $id parameter
		$ujian->ujian_id = $id;

		$data = [
			'user' 		=> $user,
			'judul'		=> 'نتائج الامتحان',
			'subjudul'	=> 'تفاصيل نتيجة الامتحان',
			'ujian'     => $ujian,
			'hasil'     => $hasil,
			'mhs'       => $mhs
		];

		$this->load->view('_templates/dashboard/_header.php', $data);
		$this->load->view('ujian/results', $data);
		$this->load->view('_templates/dashboard/_footer.php');
	}
public function quiz($id)
{
    $this->load->library('Pdf');

		$user = $this->ion_auth->user()->row();
		$mhs 	= $this->ujian->getIdMahasiswa($user->username);
		$hasil 	= $this->ujian->HslUjian($id, $mhs->id_mahasiswa)->row();
		$ujian 	= $this->ujian->getUjianById($id);

    // Assigning ujian_id to match the $id parameter
    $ujian->ujian_id = $id;

    $data = [

        'ujian'     => $ujian,
        'hasil'     => $hasil,
        'mhs'       => $mhs
    ];

$this->load->view('ujian/quiz', $data);

}
	
	public function cetak_detail($id)
	{
		$this->load->library('Pdf');

		$ujian = $this->ujian->getUjianById($id);
		$nilai = $this->ujian->bandingNilai($id);
		$hasil = $this->ujian->HslUjianById($id)->result();

		$data = [
			'ujian'	=> $ujian,
			'nilai'	=> $nilai,
			'hasil'	=> $hasil
		];

		$this->load->view('ujian/cetak_detail', $data);
	}
	
}